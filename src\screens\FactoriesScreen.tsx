import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  Alert,
  ActivityIndicator,
  TextInput,
  Modal,
} from 'react-native';
import {
  Factory,
  Plus,
  X,
  Users,
  TrendingUp,
  Settings,
  DollarSign,
  UserX,
} from 'lucide-react-native';
import { useAuthStore } from '../store/useAuthStore';
import { useAuthGuard } from '../hooks/useAuthGuard';
import { factoryService } from '../services/factoryService';
import { FactoryType, WageType } from '../types/factory';
import { showErrorToast, showSuccessToast } from '../utils/toastUtils';

interface Props {
  navigation: any;
}


export const FactoriesScreen: React.FC<Props> = ({ navigation }) => {
  useAuthGuard({ navigation });
  
  const { user } = useAuthStore();
  const [factories, setFactories] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [workingFactories, setWorkingFactories] = useState<Set<string>>(new Set());
  const [showFactoryDetails, setShowFactoryDetails] = useState(false);
  const [selectedFactory, setSelectedFactory] = useState<any>(null);
  const [factoryWorkers, setFactoryWorkers] = useState<any[]>([]);
  const [createModalOpen, setCreateModalOpen] = useState(false);
  const [workerModalOpen, setWorkerModalOpen] = useState(false);
  const [defaultWageModalOpen, setDefaultWageModalOpen] = useState(false);
  const [updatingWorkerWage, setUpdatingWorkerWage] = useState(false);
  const [updatingDefaultWage, setUpdatingDefaultWage] = useState(false);
  
  // Forms state
  const [createForm, setCreateForm] = useState({
    name: '',
    type: FactoryType.GOLD,
    regionId: '',
    wage: 50,
    wageType: WageType.PERCENTAGE
  });
  
  const [workerWageForm, setWorkerWageForm] = useState({
    workerId: '',
    wage: 0
  });
  
  const [defaultWageForm, setDefaultWageForm] = useState({
    wage: 0
  });

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setLoading(true);

      if (user?.region?.id) {
        const factoriesData = await factoryService.getFactoriesInUserRegion();
        setFactories(factoriesData || []);
      } else {
        setFactories([]);
      }
    } catch (error: any) {
      console.error('Error fetching factory data:', error);
      if (!error?.message?.includes('region')) {
        showErrorToast('Failed to fetch factory data');
      }
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const loadFactoryDetails = async (factoryId: string) => {
    try {
      const workers = await factoryService.getFactoryWorkers(Number(factoryId));
      setFactoryWorkers(workers || []);
    } catch (err: any) {
      showErrorToast('Failed to load factory details');
    }
  };

  const handleWork = async (factoryId: string) => {
    if (workingFactories.has(factoryId)) return;

    try {
      setWorkingFactories(prev => new Set(prev).add(factoryId));
      
      const currentEnergy = user?.energy || 0;
      const response = await factoryService.workAtFactory(Number(factoryId), currentEnergy);
      
      showSuccessToast('Work session completed successfully');
      
      await fetchData();
    } catch (error: any) {
      showErrorToast(error || 'Failed to complete work session');
    } finally {
      setWorkingFactories(prev => {
        const newSet = new Set(prev);
        newSet.delete(factoryId);
        return newSet;
      });
    }
  };

  const handleUpgradeFactory = async (factoryId: string) => {
    try {
      const upgradeResult = await factoryService.upgradeFactory(Number(factoryId));
      showSuccessToast('Factory upgraded successfully!');
      
      if (selectedFactory?.id === factoryId) {
        setSelectedFactory(upgradeResult.factory);
      }
      
      await fetchData();
    } catch (err: any) {
      showErrorToast(err || 'Failed to upgrade factory');
    }
  };

  const handleShutdownFactory = async (factoryId: string) => {
    Alert.alert(
      'Shutdown Factory',
      'Are you sure you want to shutdown this factory? All workers will be fired and you will get 50% refund of total costs.',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Shutdown', 
          style: 'destructive',
          onPress: async () => {
            try {
              const result = await factoryService.shutdownFactory(Number(factoryId));
              showSuccessToast(`Factory shutdown! ${result.workersFired} workers fired. Refund: ${result.refundAmount} gold`);
              await fetchData();
              setShowFactoryDetails(false);
              setSelectedFactory(null);
            } catch (err: any) {
              showErrorToast(err || 'Failed to shutdown factory');
            }
          }
        }
      ]
    );
  };

  const handleFireWorker = async (factoryId: string, workerId: string, workerName: string) => {
    Alert.alert(
      'Fire Worker',
      `Are you sure you want to fire ${workerName}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Fire', 
          style: 'destructive',
          onPress: async () => {
            try {
              const result = await factoryService.fireWorker(Number(factoryId), Number(workerId));
              showSuccessToast(result.message);
              await loadFactoryDetails(factoryId);
              await fetchData();
            } catch (err: any) {
              showErrorToast(err || 'Failed to fire worker');
            }
          }
        }
      ]
    );
  };

  const handleUpdateWorkerWage = async () => {
    setUpdatingWorkerWage(true);
    try {
      await factoryService.updateWorkerWage(Number(selectedFactory.id), Number(workerWageForm.workerId), { 
        workerId: Number(workerWageForm.workerId),
        wage: workerWageForm.wage
      });
      showSuccessToast('Worker wage updated successfully');
      setWorkerModalOpen(false);
      setWorkerWageForm({ workerId: '', wage: 0 });
      await loadFactoryDetails(selectedFactory.id);
    } catch (error: any) {
      showErrorToast(error || 'Failed to update worker wage');
    } finally {
      setUpdatingWorkerWage(false);
    }
  };

  const handleUpdateDefaultWage = async () => {
    setUpdatingDefaultWage(true);
    try {
      await factoryService.updateFactory(Number(selectedFactory.id), { 
        wage: defaultWageForm.wage
      });
      showSuccessToast('Factory default wage updated successfully!');
      setDefaultWageModalOpen(false);
      setDefaultWageForm({ wage: 0 });
      
      await fetchData();
      const updatedFactory = await factoryService.getFactory(Number(selectedFactory.id));
      setSelectedFactory(updatedFactory);
      
      if (showFactoryDetails) {
        await loadFactoryDetails(selectedFactory.id);
      }
    } catch (error: any) {
      showErrorToast(error || 'Failed to update factory default wage');
    } finally {
      setUpdatingDefaultWage(false);
    }
  };

  const handleCreateFactory = async () => {
    try {
      await factoryService.createFactory(createForm);
      showSuccessToast('Factory created successfully!');
      setCreateModalOpen(false);
      setCreateForm({
        name: '',
        type: FactoryType.GOLD,
        regionId: '',
        wage: 50,
        wageType: WageType.PERCENTAGE
      });
      await fetchData();
    } catch (error: any) {
      showErrorToast(error || 'Failed to create factory');
    }
  };

  const openCreateModal = () => {
    if (user?.region?.id) {
      setCreateForm({
        name: '',
        type: FactoryType.GOLD,
        regionId: user.region.id.toString(),
        wage: 50,
        wageType: WageType.PERCENTAGE
      });
    }
    setCreateModalOpen(true);
  };

  const openFactoryDetails = async (factory: any) => {
    setSelectedFactory(factory);
    await loadFactoryDetails(factory.id);
    setShowFactoryDetails(true);
  };

  const isFactoryOwner = (factory: any) => {
    return user && factory.ownerId === user.id;
  };

  const getWageDisplay = (factory: any) => {
    return `${factory.wage}%`;
  };

  const calculateRequiredExperience = (level: number) => {
    return Math.pow(2, level - 1) * 1000;
  };

  const onRefresh = () => {
    setRefreshing(true);
    fetchData();
  };

  if (loading) {
    return (
      <View className="flex-1 bg-gray-900 justify-center items-center">
        <ActivityIndicator size="large" color="#00d4ff" />
        <Text className="text-neonBlue text-xl mt-4">Loading jobs...</Text>
      </View>
    );
  }

  return (
    <View className="flex-1 bg-gray-900">
      <ScrollView
        className="flex-1"
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        <View className="p-4">
          {/* Header */}
          <View className="mb-8">
            <View className="flex-row justify-between items-center">
              <View>
                <View className="flex-row items-center">
                  <Factory width={32} height={32} color="#00d4ff" />
                  <Text className="text-3xl font-bold text-white ml-3">
                    Jobs & Factories
                  </Text>
                </View>
                <Text className="mt-2 text-gray-400">Work at factories or create your own</Text>
              </View>
              <TouchableOpacity
                onPress={openCreateModal}
                className="bg-neonBlue px-6 py-3 rounded-lg flex-row items-center"
              >
                <Plus width={20} height={20} color="#ffffff" />
                <Text className="text-white font-medium ml-2">Create Factory</Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* Energy Status */}
          <View className="bg-gray-800 rounded-lg p-6 mb-6">
            <View className="flex-row justify-between items-center">
              <Text className="text-xl font-semibold text-white">Your Energy</Text>
              <View className="flex-row items-center space-x-2">
                <View className="w-4 h-4 bg-blue-500 rounded-full" />
                <Text className="text-white text-lg">
                  {user?.energy || 0}/{user?.isPremium ? 200 : 100}
                </Text>
              </View>
            </View>
            <View className="mt-2 w-full bg-gray-700 rounded-full h-2">
              <View
                className="bg-blue-500 h-2 rounded-full"
                style={{ 
                  width: `${((user?.energy || 0) / (user?.isPremium ? 200 : 100)) * 100}%` 
                }}
              />
            </View>
          </View>

          {/* Current Employment Status */}
          {user?.workingAt && (
            <View className="bg-gray-800 rounded-lg p-6 mb-6">
              <View className="flex-row justify-between items-center">
                <View>
                  <Text className="text-xl font-semibold text-white">Current Employment</Text>
                  <Text className="text-gray-300">Working at: {user.workingAt.name}</Text>
                  <Text className="text-gray-400 text-sm">
                    Wage: {getWageDisplay(user.workingAt)}
                  </Text>
                </View>
              </View>
            </View>
          )}

          {/* Available Factories */}
          <View className="bg-gray-800 rounded-lg p-6 mb-6">
            <Text className="text-xl font-semibold text-white mb-4">Available Factories</Text>
            <View className="space-y-4">
              {factories.map((factory) => (
                <TouchableOpacity
                  key={factory.id}
                  className={`bg-gray-700 rounded-lg p-4 ${
                    factory.isPrivate ? 'border-2 border-purple-500' : ''
                  }`}
                  onPress={() => setSelectedFactory(factory)}
                >
                  <View className="flex-row justify-between items-start mb-2">
                    <Text className="text-lg font-semibold text-white">{factory.name}</Text>
                    <View className="space-y-1">
                      <View className="px-2 py-1 rounded bg-yellow-500">
                        <Text className="text-black text-sm font-bold">GOLD</Text>
                      </View>
                      {factory.isPrivate && (
                        <View className="bg-purple-600 px-2 py-1 rounded">
                          <Text className="text-white text-xs">Private</Text>
                        </View>
                      )}
                    </View>
                  </View>
                  
                  <View className="space-y-2 text-gray-300 mb-4">
                    <View className="flex-row justify-between">
                      <Text className="text-gray-400">Level:</Text>
                      <Text className="text-white">{factory.level}</Text>
                    </View>
                    <View className="flex-row justify-between">
                      <Text className="text-gray-400">Experience:</Text>
                      <Text className="text-white">{factory.experience}</Text>
                    </View>
                    <View className="flex-row justify-between">
                      <Text className="text-gray-400">Resource Bonus:</Text>
                      <Text className="text-white">{factory.resourceBonus}%</Text>
                    </View>
                    <View className="flex-row justify-between">
                      <Text className="text-gray-400">Default Wage:</Text>
                      <Text className="text-white">{getWageDisplay(factory)}</Text>
                    </View>
                    <View className="flex-row justify-between">
                      <Text className="text-gray-400">Workers:</Text>
                      <Text className="text-white">{factory.workers?.length || 0}/{factory.maxWorkers}</Text>
                    </View>
                  </View>

                  <View className="space-y-2">
                    <TouchableOpacity
                      onPress={() => handleWork(factory.id)}
                      disabled={!user || workingFactories.has(factory.id)}
                      className={`w-full px-4 py-2 rounded-md ${
                        !user || workingFactories.has(factory.id)
                          ? 'bg-gray-500'
                          : 'bg-neonBlue'
                      }`}
                    >
                      <Text className="text-white text-center font-medium">
                        {workingFactories.has(factory.id) ? 'Working...' : 'Work Here'}
                      </Text>
                    </TouchableOpacity>

                    <TouchableOpacity
                      onPress={() => openFactoryDetails(factory)}
                      className="w-full px-3 py-2 bg-gray-600 rounded"
                    >
                      <Text className="text-white text-center text-sm">Details</Text>
                    </TouchableOpacity>
                  </View>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        </View>
      </ScrollView>

      {/* Factory Details Modal */}
      <Modal
        visible={showFactoryDetails}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setShowFactoryDetails(false)}
      >
        <View className="flex-1 bg-black bg-opacity-50 justify-center items-center p-4">
          <View className="bg-gray-800 rounded-lg max-w-full w-full max-h-[90%] border border-gray-700">
            <ScrollView showsVerticalScrollIndicator={false}>
              <View className="p-6">
                <View className="flex-row justify-between items-center mb-6">
                  <View className="flex-row items-center">
                    <Factory width={24} height={24} color="#00d4ff" />
                    <Text className="text-2xl font-bold text-white ml-2">
                      {selectedFactory?.name} - Factory Management
                    </Text>
                  </View>
                  <TouchableOpacity
                    onPress={() => setShowFactoryDetails(false)}
                    className="p-1"
                  >
                    <X width={24} height={24} color="#9ca3af" />
                  </TouchableOpacity>
                </View>

                {/* Factory Overview */}
                <View className="space-y-4 mb-6">
                  <View className="bg-gray-700 p-4 rounded">
                    <View className="flex-row items-center mb-3">
                      <DollarSign width={16} height={16} color="#00d4ff" />
                      <Text className="font-semibold text-white ml-2">Basic Info</Text>
                    </View>
                    <View className="space-y-2">
                      <View className="flex-row justify-between">
                        <Text className="text-gray-400">Type:</Text>
                        <View className="px-2 py-1 rounded bg-yellow-500">
                          <Text className="text-black text-xs font-bold">
                            {selectedFactory?.type || 'GOLD'}
                          </Text>
                        </View>
                      </View>
                      <View className="flex-row justify-between">
                        <Text className="text-gray-400">Level:</Text>
                        <Text className="text-white font-semibold">{selectedFactory?.level}</Text>
                      </View>
                      <View className="flex-row justify-between">
                        <Text className="text-gray-400">Experience:</Text>
                        <Text className="text-white">{selectedFactory?.experience?.toLocaleString()}</Text>
                      </View>
                      <View className="flex-row justify-between">
                        <Text className="text-gray-400">Resource Bonus:</Text>
                        <Text className="text-green-400">+{selectedFactory?.resourceBonus}%</Text>
                      </View>
                    </View>
                  </View>

                  <View className="bg-gray-700 p-4 rounded">
                    <View className="flex-row items-center mb-3">
                      <Users width={16} height={16} color="#00d4ff" />
                      <Text className="font-semibold text-white ml-2">Workers</Text>
                    </View>
                    <View className="space-y-2">
                      <View className="flex-row justify-between">
                        <Text className="text-gray-400">Current:</Text>
                        <Text className="text-white">{selectedFactory?.workers?.length || 0}</Text>
                      </View>
                      <View className="flex-row justify-between">
                        <Text className="text-gray-400">Maximum:</Text>
                        <Text className="text-white">{selectedFactory?.maxWorkers}</Text>
                      </View>
                      <View className="flex-row justify-between">
                        <Text className="text-gray-400">Available:</Text>
                        <Text className="text-green-400">
                          {(selectedFactory?.maxWorkers || 0) - (selectedFactory?.workers?.length || 0)}
                        </Text>
                      </View>
                    </View>
                  </View>
                </View>

                {/* Experience Progress Bar */}
                {selectedFactory && selectedFactory.level < 100 && (
                  <View className="bg-gray-700 p-4 rounded mb-6">
                    <View className="flex-row justify-between items-center mb-2">
                      <Text className="font-semibold text-white">Experience Progress</Text>
                      <Text className="text-sm text-gray-400">
                        {selectedFactory.experience?.toLocaleString()} / {calculateRequiredExperience(selectedFactory.level)} XP
                      </Text>
                    </View>
                    <View className="w-full bg-gray-600 rounded-full h-2">
                      <View
                        className="bg-blue-500 h-2 rounded-full"
                        style={{ 
                          width: `${Math.min((selectedFactory.experience / calculateRequiredExperience(selectedFactory.level)) * 100, 100)}%` 
                        }}
                      />
                    </View>
                    <Text className="text-xs text-gray-400 mt-1">
                      {selectedFactory.experience >= calculateRequiredExperience(selectedFactory.level) 
                        ? 'Ready to upgrade!' 
                        : `Need ${(calculateRequiredExperience(selectedFactory.level) - selectedFactory.experience).toLocaleString()} more XP to upgrade`
                      }
                    </Text>
                  </View>
                )}

                {/* Owner Actions */}
                {selectedFactory && isFactoryOwner(selectedFactory) && (
                  <View className="bg-gray-700 p-4 rounded mb-6">
                    <Text className="font-semibold mb-3 text-white">Owner Actions</Text>
                    <View className="space-y-3">
                      {selectedFactory.level < 100 && selectedFactory.experience >= calculateRequiredExperience(selectedFactory.level) && (
                        <TouchableOpacity
                          onPress={() => handleUpgradeFactory(selectedFactory.id)}
                          className="bg-yellow-600 px-4 py-2 rounded flex-row items-center"
                        >
                          <TrendingUp width={16} height={16} color="#ffffff" />
                          <Text className="text-white text-sm ml-2">Upgrade Factory (35 Gold)</Text>
                        </TouchableOpacity>
                      )}
                      <TouchableOpacity
                        onPress={() => {
                          setDefaultWageForm({ wage: selectedFactory.wage });
                          setDefaultWageModalOpen(true);
                        }}
                        className="bg-blue-600 px-4 py-2 rounded flex-row items-center"
                      >
                        <DollarSign width={16} height={16} color="#ffffff" />
                        <Text className="text-white text-sm ml-2">Update Default Wage</Text>
                      </TouchableOpacity>
                      <TouchableOpacity
                        onPress={() => handleShutdownFactory(selectedFactory.id)}
                        className="bg-red-600 px-4 py-2 rounded flex-row items-center"
                      >
                        <Settings width={16} height={16} color="#ffffff" />
                        <Text className="text-white text-sm ml-2">Shutdown Factory</Text>
                      </TouchableOpacity>
                    </View>
                  </View>
                )}

                {/* Workers Table */}
                <View className="bg-gray-700 rounded overflow-hidden">
                  <View className="px-4 py-3 border-b border-gray-600">
                    <View className="flex-row items-center">
                      <Users width={16} height={16} color="#00d4ff" />
                      <Text className="font-semibold text-white ml-2">
                        Workers ({factoryWorkers.length})
                      </Text>
                    </View>
                  </View>
                  {factoryWorkers.length > 0 ? (
                    <View className="p-4 space-y-4">
                      {factoryWorkers.map((worker) => (
                        <View key={worker.id} className="bg-gray-600 p-3 rounded">
                          <View className="flex-row justify-between items-start mb-2">
                            <Text className="text-sm font-medium text-white">
                              {worker.worker?.username || `User ${worker.workerId}`}
                            </Text>
                            <View className="space-y-1">
                              {selectedFactory && isFactoryOwner(selectedFactory) && (
                                <View className="px-2 py-1 rounded bg-green-500">
                                  <Text className="text-white text-xs">{worker.wage}%</Text>
                                </View>
                              )}
                              <View className={`px-2 py-1 rounded ${
                                worker.isActive ? 'bg-green-500' : 'bg-gray-500'
                              }`}>
                                <Text className="text-white text-xs">
                                  {worker.isActive ? 'Active' : 'Inactive'}
                                </Text>
                              </View>
                            </View>
                          </View>
                          <Text className="text-sm text-gray-300 mb-2">
                            Joined: {new Date(worker.createdAt).toLocaleDateString()}
                          </Text>
                          {selectedFactory && isFactoryOwner(selectedFactory) && (
                            <View className="flex-row space-x-2">
                              <TouchableOpacity
                                onPress={() => {
                                  setWorkerWageForm({
                                    workerId: worker.workerId,
                                    wage: worker.wage
                                  });
                                  setWorkerModalOpen(true);
                                }}
                                className="bg-blue-600 px-3 py-1 rounded"
                              >
                                <Text className="text-white text-xs">Edit Wage</Text>
                              </TouchableOpacity>
                              {worker.isActive && (
                                <TouchableOpacity
                                  onPress={() => handleFireWorker(
                                    selectedFactory.id, 
                                    worker.workerId, 
                                    worker.worker?.username || `User ${worker.workerId}`
                                  )}
                                  className="bg-red-600 px-3 py-1 rounded flex-row items-center"
                                >
                                  <UserX width={12} height={12} color="#ffffff" />
                                  <Text className="text-white text-xs ml-1">Fire</Text>
                                </TouchableOpacity>
                              )}
                            </View>
                          )}
                        </View>
                      ))}
                    </View>
                  ) : (
                    <View className="px-4 py-8 items-center">
                      <Users width={48} height={48} color="#6b7280" />
                      <Text className="text-gray-400 mt-3">No workers yet</Text>
                      <Text className="text-sm text-gray-500 text-center">
                        Workers will appear here when they start working at this factory
                      </Text>
                    </View>
                  )}
                </View>
              </View>
            </ScrollView>
          </View>
        </View>
      </Modal>

      {/* Create Factory Modal */}
      <Modal
        visible={createModalOpen}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setCreateModalOpen(false)}
      >
        <View className="flex-1 bg-black bg-opacity-50 justify-center items-center p-4">
          <View className="bg-gray-800 rounded-lg max-w-md w-full p-6 border border-gray-700">
            <View className="flex-row justify-between items-center mb-4">
              <View className="flex-row items-center">
                <Factory width={20} height={20} color="#00d4ff" />
                <Text className="text-xl font-semibold text-white ml-2">Create New Factory</Text>
              </View>
              <TouchableOpacity
                onPress={() => setCreateModalOpen(false)}
                className="p-1"
              >
                <X width={24} height={24} color="#9ca3af" />
              </TouchableOpacity>
            </View>
            
            <View className="space-y-4">
              <View>
                <Text className="text-sm font-medium text-gray-300 mb-1">Factory Name</Text>
                <TextInput
                  value={createForm.name}
                  onChangeText={(text) => setCreateForm({...createForm, name: text})}
                  className="w-full border border-gray-600 rounded-md px-3 py-2 bg-gray-700 text-white"
                  placeholder="Enter factory name"
                  placeholderTextColor="#9ca3af"
                />
              </View>

              <View>
                <Text className="text-sm font-medium text-gray-300 mb-1">Region</Text>
                <TextInput
                  value={user?.region?.name || 'Unknown Region'}
                  className="w-full border border-gray-600 rounded-md px-3 py-2 bg-gray-600 text-gray-300"
                  editable={false}
                />
                <Text className="text-xs text-gray-400 mt-1">
                  Factory will be created in your current region
                </Text>
              </View>

              <View>
                <Text className="text-sm font-medium text-gray-300 mb-1">Worker Wage (%)</Text>
                <TextInput
                  value={createForm.wage.toString()}
                  onChangeText={(text) => setCreateForm({...createForm, wage: parseFloat(text) || 0})}
                  className="w-full border border-gray-600 rounded-md px-3 py-2 bg-gray-700 text-white"
                  keyboardType="numeric"
                  placeholder="50"
                  placeholderTextColor="#9ca3af"
                />
                <Text className="text-xs text-gray-400 mt-1">
                  Percentage of additional resources (money, ore) workers will earn. Gold goes 100% to workers.
                </Text>
              </View>

              <View className="bg-yellow-900 border border-yellow-700 rounded-md p-3">
                <Text className="text-sm text-yellow-400">
                  <Text className="font-bold">Creation Cost:</Text> 200 Gold
                </Text>
              </View>

              <View className="flex-row space-x-3">
                <TouchableOpacity
                  onPress={() => setCreateModalOpen(false)}
                  className="flex-1 bg-gray-600 py-2 px-4 rounded-md"
                >
                  <Text className="text-white font-medium text-center">Cancel</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  onPress={handleCreateFactory}
                  className="flex-1 bg-neonBlue py-2 px-4 rounded-md"
                >
                  <Text className="text-white font-medium text-center">Create Factory</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </View>
      </Modal>

      {/* Worker Wage Modal */}
      <Modal
        visible={workerModalOpen}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setWorkerModalOpen(false)}
      >
        <View className="flex-1 bg-black bg-opacity-50 justify-center items-center p-4">
          <View className="bg-gray-800 rounded-lg max-w-md w-full p-6 border border-gray-700">
            <View className="flex-row justify-between items-center mb-4">
              <View className="flex-row items-center">
                <Users width={20} height={20} color="#00d4ff" />
                <Text className="text-xl font-semibold text-white ml-2">Update Worker Wage</Text>
              </View>
              <TouchableOpacity
                onPress={() => setWorkerModalOpen(false)}
                className="p-1"
              >
                <X width={24} height={24} color="#9ca3af" />
              </TouchableOpacity>
            </View>
            
            <View className="bg-gray-700 p-3 rounded mb-4">
              <Text className="text-sm text-gray-300">
                <Text className="font-bold">Factory:</Text> {selectedFactory?.name}
              </Text>
              <Text className="text-sm text-gray-300">
                <Text className="font-bold">Worker:</Text> {factoryWorkers.find(w => w.workerId === workerWageForm.workerId)?.worker?.username || `User ${workerWageForm.workerId}`}
              </Text>
            </View>

            <View className="space-y-4">
              <View>
                <Text className="text-sm font-medium text-gray-300 mb-1">Worker Wage (%)</Text>
                <TextInput
                  value={workerWageForm.wage.toString()}
                  onChangeText={(text) => setWorkerWageForm({...workerWageForm, wage: parseFloat(text) || 0})}
                  className="w-full border border-gray-600 rounded-md px-3 py-2 bg-gray-700 text-white"
                  keyboardType="numeric"
                />
                <Text className="text-xs text-gray-400 mt-1">
                  Percentage of additional resources (money, ore) the worker will earn. Gold goes 100% to workers.
                </Text>
              </View>

              <View className="bg-blue-900 border border-blue-700 rounded-md p-3">
                <Text className="text-sm text-blue-400">
                  <Text className="font-bold">Current Wage:</Text> {workerWageForm.wage}%
                </Text>
              </View>

              <View className="flex-row space-x-3">
                <TouchableOpacity
                  onPress={() => setWorkerModalOpen(false)}
                  className="flex-1 bg-gray-600 py-2 px-4 rounded-md"
                >
                  <Text className="text-white font-medium text-center">Cancel</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  onPress={handleUpdateWorkerWage}
                  disabled={updatingWorkerWage}
                  className="flex-1 bg-green-600 py-2 px-4 rounded-md"
                >
                  <Text className="text-white font-medium text-center">
                    {updatingWorkerWage ? 'Updating...' : 'Update Wage'}
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </View>
      </Modal>

      {/* Default Wage Modal */}
      <Modal
        visible={defaultWageModalOpen}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setDefaultWageModalOpen(false)}
      >
        <View className="flex-1 bg-black bg-opacity-50 justify-center items-center p-4">
          <View className="bg-gray-800 rounded-lg max-w-md w-full p-6 border border-gray-700">
            <View className="flex-row justify-between items-center mb-4">
              <View className="flex-row items-center">
                <DollarSign width={20} height={20} color="#00d4ff" />
                <Text className="text-xl font-semibold text-white ml-2">Update Factory Default Wage</Text>
              </View>
              <TouchableOpacity
                onPress={() => {
                  setDefaultWageModalOpen(false);
                  setDefaultWageForm({ wage: 0 });
                }}
                className="p-1"
              >
                <X width={24} height={24} color="#9ca3af" />
              </TouchableOpacity>
            </View>
            
            <View className="bg-gray-700 p-3 rounded mb-4">
              <Text className="text-sm text-gray-300">
                <Text className="font-bold">Factory:</Text> {selectedFactory?.name}
              </Text>
              <Text className="text-sm text-gray-300">
                <Text className="font-bold">Current Default Wage:</Text> {selectedFactory?.wage}%
              </Text>
            </View>

            <View className="space-y-4">
              <View>
                <Text className="text-sm font-medium text-gray-300 mb-1">New Default Wage (%)</Text>
                <TextInput
                  value={(defaultWageForm.wage || selectedFactory?.wage || 0).toString()}
                  onChangeText={(text) => setDefaultWageForm({...defaultWageForm, wage: parseFloat(text) || 0})}
                  className="w-full border border-gray-600 rounded-md px-3 py-2 bg-gray-700 text-white"
                  keyboardType="numeric"
                />
                <Text className="text-xs text-gray-400 mt-1">
                  This will be the default wage for new workers joining the factory. Existing workers keep their individual wages.
                </Text>
              </View>

              <View className="bg-blue-900 border border-blue-700 rounded-md p-3">
                <Text className="text-sm text-blue-400">
                  <Text className="font-bold">New Default Wage:</Text> {defaultWageForm.wage || selectedFactory?.wage || 0}%
                </Text>
              </View>

              <View className="flex-row space-x-3">
                <TouchableOpacity
                  onPress={() => {
                    setDefaultWageModalOpen(false);
                    setDefaultWageForm({ wage: 0 });
                  }}
                  className="flex-1 bg-gray-600 py-2 px-4 rounded-md"
                >
                  <Text className="text-white font-medium text-center">Cancel</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  onPress={handleUpdateDefaultWage}
                  disabled={updatingDefaultWage}
                  className="flex-1 bg-blue-600 py-2 px-4 rounded-md"
                >
                  <Text className="text-white font-medium text-center">
                    {updatingDefaultWage ? 'Updating...' : 'Update Default Wage'}
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};